CREATE TABLE `goods_rank` (
                              `id` int(11) NOT NULL AUTO_INCREMENT,
                              `source_type` int(11) DEFAULT NULL COMMENT '来源类型 8 - 周榜, 9 - 月榜, 10 - 季度, 11 - 上1季度 , 12 - 上2季度, 13 - 上3季度',
                              `goods_id` bigint(11) DEFAULT NULL COMMENT '模板商品id',
                              `hot` int(11) DEFAULT NULL COMMENT '热度',
                              `rank_type` int(2) DEFAULT NULL COMMENT '0-全量商品 1-无负向商品',
                              `rank_seq` int(11) DEFAULT NULL COMMENT '榜单排名',
                              `status` int(2) DEFAULT NULL COMMENT ' 状态 0草稿 1开放 2自动关闭 3人工关闭',
                              `generate_goods_count` int(11) DEFAULT NULL COMMENT '生成商品数',
                              `goods_name` varchar(255) DEFAULT NULL COMMENT '商品名称',
                              `category_id` int(11) DEFAULT NULL COMMENT '类目id',
                              `main_image` varchar(2000) DEFAULT NULL COMMENT '商品主图',
                              `sample_url` varchar(1024) DEFAULT '' COMMENT '样例链接',
                              `sample_video_url` varchar(1024) DEFAULT '' COMMENT '样例视频',
                              `min_price` decimal(10,2) DEFAULT NULL COMMENT '最低销售价格',
                              `max_price` decimal(10,2) DEFAULT NULL COMMENT '最高销售价格',
                              `is_del` int(2) DEFAULT NULL comment '操作类型: 0 - 入榜 ， 1 - 出榜',
                              `create_time` datetime DEFAULT NULL,
                              `update_time` datetime DEFAULT NULL,
                              PRIMARY KEY (`id`),
                              KEY `goods_id_idx` (`goods_id`) USING BTREE,
                              KEY `idx_source_type_goods_id` (`source_type`,`goods_id`),
                              KEY `idx_source_type_category_id` (`source_type`,`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品排行榜';

CREATE TABLE `goods_rank_history` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                      `rank_id` int(11) DEFAULT NULL COMMENT '榜单记录id',
                                      `source_type` int(2) DEFAULT NULL COMMENT '类型 8 - 周榜, 9 - 月榜, 10 - 季度, 11 - 上1季度 , 12 - 上2季度, 13 - 上3季度',
                                      `rank_type` int(2) DEFAULT NULL COMMENT '全量榜单 0， 去掉负商品榜单 1',
                                      `goods_id` bigint(11) DEFAULT NULL COMMENT '榜单商品id',
                                      `operation_type` int(11) DEFAULT NULL COMMENT '操作类型 0-进入 1-退出',
                                      `operation_time` datetime DEFAULT NULL,
                                      `create_time` datetime DEFAULT NULL,
                                      `update_time` datetime DEFAULT NULL,
                                      PRIMARY KEY (`id`),
                                      KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品排行榜出入榜历史';

# 新生成的goods_id对应的记录。goods_id唯一。 source_goods_id不唯一。排行榜id可以保存。留作记录。
CREATE TABLE `goods_rank_gen_record` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                         `rank_id` int(11) NOT NULL COMMENT '排行榜id',
                                         `source_goods_id` bigint(11) NOT NULL COMMENT '来源商品id',
                                         `goods_id` bigint(11) NOT NULL COMMENT '生成商品id',
                                         `shop_id` int(11) NOT NULL COMMENT '店铺id',
                                         `is_del` int(2) DEFAULT NULL COMMENT '是否删除',
                                         `create_time` datetime DEFAULT NULL COMMENT '生成时间',
                                         `create_user` varchar(255) DEFAULT NULL COMMENT '生成操作人',
                                         PRIMARY KEY (`id`),
                                         KEY `idx_goods_id` (`goods_id`) USING BTREE,
                                         KEY `idx_source_goods_id` (`source_goods_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排行榜生成商品记录';